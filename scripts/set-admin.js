const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function setAdmin() {
  const email = process.argv[2];
  
  if (!email) {
    console.error('Please provide an email address');
    console.log('Usage: node scripts/set-admin.js <EMAIL>');
    process.exit(1);
  }

  try {
    const user = await prisma.user.findUnique({
      where: { email: email }
    });

    if (!user) {
      console.error(`User with email ${email} not found`);
      console.log('Make sure the user has signed up first');
      process.exit(1);
    }

    const updatedUser = await prisma.user.update({
      where: { email: email },
      data: { role: 'ADMIN' }
    });

    console.log(`✅ Successfully set ${updatedUser.firstName} ${updatedUser.lastName} (${updatedUser.email}) as ADMIN`);
  } catch (error) {
    console.error('Error setting admin:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

setAdmin();
