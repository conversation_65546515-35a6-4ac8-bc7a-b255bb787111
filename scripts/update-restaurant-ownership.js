const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function updateRestaurantOwnership() {
  try {
    // Get all restaurants without owners
    const restaurants = await prisma.restaurant.findMany({
      where: { ownerId: null }
    });

    console.log(`Found ${restaurants.length} restaurants without owners`);

    // Update all restaurants to be published (since they're seeded data)
    await prisma.restaurant.updateMany({
      where: { ownerId: null },
      data: { 
        isPublished: true,
        ownerId: null // Keep them as admin-owned (no specific owner)
      }
    });

    console.log('✅ Updated all seeded restaurants to be published');
    console.log('✅ All seeded restaurants are now admin-owned (no specific owner)');
  } catch (error) {
    console.error('Error updating restaurant ownership:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

updateRestaurantOwnership();
