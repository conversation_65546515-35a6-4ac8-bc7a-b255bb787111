This is a [Next.js](https://nextjs.org) project bootstrapped with [`create-next-app`](https://github.com/vercel/next.js/tree/canary/packages/create-next-app).

## Getting Started

First, run the development server:

```bash
npm run dev
# or
yarn dev
# or
pnpm dev
# or
bun dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

You can start editing the page by modifying `app/page.js`. The page auto-updates as you edit the file.

This project uses [`next/font`](https://nextjs.org/docs/app/building-your-application/optimizing/fonts) to automatically optimize and load [Geist](https://vercel.com/font), a new font family for Vercel.

## Learn More

To learn more about Next.js, take a look at the following resources:

- [Next.js Documentation](https://nextjs.org/docs) - learn about Next.js features and API.
- [Learn Next.js](https://nextjs.org/learn) - an interactive Next.js tutorial.

You can check out [the Next.js GitHub repository](https://github.com/vercel/next.js) - your feedback and contributions are welcome!

## Deploy on Vercel

The easiest way to deploy your Next.js app is to use the [Vercel Platform](https://vercel.com/new?utm_medium=default-template&filter=next.js&utm_source=create-next-app&utm_campaign=create-next-app-readme) from the creators of Next.js.

Check out our [Next.js deployment documentation](https://nextjs.org/docs/app/building-your-application/deploying) for more details.

```
# Seed the database
npm run db:seed

# Reset database and re-seed
npm run db:reset

# Or use Prisma directly
npx prisma db seed
```

Set Admin Script

```
npm run set-admin <EMAIL>
```

Database seeding

```
npm run db:seed
npm run db:reset
```

🚀 Testing Instructions
Test Edit Functionality:
Sign in and add a restaurant
Visit the restaurant details page
Click "Edit Restaurant" button
Modify details and save
Test Delete Functionality:
Set yourself as admin: npm run set-admin <EMAIL>
Go to any restaurant edit page
Click "Delete" button (admin only)
Test Pagination:
Visit /restaurants to see paginated list
Use search with many results
Navigate between pages
Test 404 Page:
Visit any non-existent URL
Try the action buttons
Test Footer:
Scroll to bottom of any page
Click footer links
