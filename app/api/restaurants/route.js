import { NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { restaurantSchema } from "@/utils/schema";
import getGeolocation from "@/lib/geocoding";

// function to create restaurant
export async function POST(request) {
  try {
    const data = await request.json();

    // validate the request body
    const validationResult = restaurantSchema.safeParse(data);
    if (!validationResult.success) {
      console.log(validationResult.error.format());
      return NextResponse.json(
        { error: "something went wrong, not proper inputs, check console" },
        { status: 400 }
      );
    }

    // get geolocation from address
    const geolocation = await getGeolocation(
      data.address,
      data.city,
      data.state
    );

    const restaurant = await prisma.restaurant.create({
      data: {
        name: data.name,
        address: data.address,
        city: data.city,
        state: data.state,
        zipcode: data.zipcode,
        type: data.type || "Dine-in",
        phone: data.phone || null,
        website: data.website || null,
        cuisines: data.cuisines || [],
        highlights: data.highlights || [],
        lat: geolocation.lat || null,
        lng: geolocation.lng || null,
        slug: data.slug,
      },
    });

    return NextResponse.json(restaurant, { status: 201 });
  } catch (error) {
    console.error("Error creating restaurant:", error);
    return NextResponse.json(
      { error: "Failed to create restaurant" },
      { status: 500 }
    );
  }
}
