import { NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { getCurrentUser, requireAuth } from "@/lib/auth";
import { restaurantSchema } from "@/utils/schema";
import getGeolocation from "@/lib/geocoding";

// GET /api/restaurants/[id] - Get restaurant by ID
export async function GET(request, { params }) {
  try {
    const { id } = await params;

    const restaurant = await prisma.restaurant.findUnique({
      where: { id },
      include: {
        owner: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true
          }
        },
        _count: {
          select: {
            favorites: true
          }
        }
      }
    });

    if (!restaurant) {
      return NextResponse.json({ error: "Restaurant not found" }, { status: 404 });
    }

    return NextResponse.json(restaurant);
  } catch (error) {
    console.error("Error fetching restaurant:", error);
    return NextResponse.json(
      { error: "Failed to fetch restaurant" },
      { status: 500 }
    );
  }
}

// PUT /api/restaurants/[id] - Update restaurant
export async function PUT(request, { params }) {
  try {
    const user = await requireAuth();
    const { id } = await params;
    const data = await request.json();

    // Get the restaurant to check ownership
    const restaurant = await prisma.restaurant.findUnique({
      where: { id },
      select: { id: true, ownerId: true, slug: true }
    });

    if (!restaurant) {
      return NextResponse.json({ error: "Restaurant not found" }, { status: 404 });
    }

    // Check permissions: Admin can edit all, Owner can edit their own
    const canEdit = user.role === 'ADMIN' || 
                    (user.role === 'OWNER' && restaurant.ownerId === user.id) ||
                    (restaurant.ownerId === user.id);

    if (!canEdit) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 403 });
    }

    // Validate the request body (excluding slug for updates)
    const updateData = {
      name: data.name,
      address: data.address,
      city: data.city,
      state: data.state,
      zipcode: data.zipcode,
      type: data.type || "Dine-in",
      phone: data.phone || null,
      website: data.website || null,
      cuisines: data.cuisines || [],
      highlights: data.highlights || []
    };

    // Only admin can change publication and featured status
    if (user.role === 'ADMIN') {
      if (data.hasOwnProperty('isPublished')) {
        updateData.isPublished = data.isPublished;
      }
      if (data.hasOwnProperty('isFeatured')) {
        updateData.isFeatured = data.isFeatured;
      }
    } else if (restaurant.ownerId === user.id) {
      // Owners can publish/unpublish their own restaurants
      if (data.hasOwnProperty('isPublished')) {
        updateData.isPublished = data.isPublished;
      }
    }

    // Get geolocation if address changed
    let geolocation = { lat: null, lng: null };
    try {
      geolocation = await getGeolocation(updateData.address, updateData.city, updateData.state);
    } catch (error) {
      console.warn("Geocoding failed:", error);
    }

    updateData.lat = geolocation.lat;
    updateData.lng = geolocation.lng;

    const updatedRestaurant = await prisma.restaurant.update({
      where: { id },
      data: updateData,
      include: {
        owner: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true
          }
        }
      }
    });

    return NextResponse.json(updatedRestaurant);
  } catch (error) {
    console.error("Error updating restaurant:", error);
    
    if (error.message.includes('required')) {
      return NextResponse.json({ error: "Authentication required" }, { status: 401 });
    }
    
    return NextResponse.json(
      { error: "Failed to update restaurant" },
      { status: 500 }
    );
  }
}

// DELETE /api/restaurants/[id] - Delete restaurant (Admin only)
export async function DELETE(request, { params }) {
  try {
    const user = await requireAuth();
    const { id } = await params;

    // Only admins can delete restaurants
    if (user.role !== 'ADMIN') {
      return NextResponse.json({ error: "Admin access required" }, { status: 403 });
    }

    // Check if restaurant exists
    const restaurant = await prisma.restaurant.findUnique({
      where: { id },
      select: { id: true, name: true }
    });

    if (!restaurant) {
      return NextResponse.json({ error: "Restaurant not found" }, { status: 404 });
    }

    // Delete the restaurant (this will cascade delete favorites due to schema)
    await prisma.restaurant.delete({
      where: { id }
    });

    return NextResponse.json({ 
      message: "Restaurant deleted successfully",
      deletedRestaurant: restaurant
    });
  } catch (error) {
    console.error("Error deleting restaurant:", error);
    
    if (error.message.includes('required')) {
      return NextResponse.json({ error: "Authentication required" }, { status: 401 });
    }
    
    return NextResponse.json(
      { error: "Failed to delete restaurant" },
      { status: 500 }
    );
  }
}
