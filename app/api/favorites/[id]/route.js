import { NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { getCurrentUser } from "@/lib/auth";

// DELETE /api/favorites/[id] - Remove from favorites
export async function DELETE(request, { params }) {
  try {
    const user = await getCurrentUser();
    
    if (!user) {
      return NextResponse.json({ error: "Authentication required" }, { status: 401 });
    }

    const { id } = await params;

    // Find the favorite
    const favorite = await prisma.favorite.findUnique({
      where: { id: id }
    });

    if (!favorite) {
      return NextResponse.json({ error: "Favorite not found" }, { status: 404 });
    }

    // Check if user owns this favorite
    if (favorite.userId !== user.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 403 });
    }

    // Delete the favorite
    await prisma.favorite.delete({
      where: { id: id }
    });

    return NextResponse.json({ message: "Favorite removed successfully" });
  } catch (error) {
    console.error("Error removing favorite:", error);
    return NextResponse.json(
      { error: "Failed to remove favorite" },
      { status: 500 }
    );
  }
}

// Alternative endpoint to remove by restaurant ID
// DELETE /api/favorites/restaurant/[restaurantId]
export async function removeByRestaurantId(restaurantId, userId) {
  try {
    const favorite = await prisma.favorite.findUnique({
      where: {
        userId_restaurantId: {
          userId: userId,
          restaurantId: restaurantId
        }
      }
    });

    if (favorite) {
      await prisma.favorite.delete({
        where: { id: favorite.id }
      });
    }

    return true;
  } catch (error) {
    console.error("Error removing favorite by restaurant ID:", error);
    return false;
  }
}
