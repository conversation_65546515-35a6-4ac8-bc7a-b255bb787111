import { NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { requireAdmin } from "@/lib/auth";

// PUT /api/admin/users/[id]/role - Update user role
export async function PUT(request, { params }) {
  try {
    await requireAdmin();
    
    const { id } = await params;
    const { role } = await request.json();

    // Validate role
    const validRoles = ['USER', 'OWNER', 'ADMIN'];
    if (!validRoles.includes(role)) {
      return NextResponse.json({ error: "Invalid role" }, { status: 400 });
    }

    // Update user role
    const user = await prisma.user.update({
      where: { id: id },
      data: { role: role }
    });

    return NextResponse.json(user);
  } catch (error) {
    console.error("Error updating user role:", error);
    
    if (error.message.includes('required')) {
      return NextResponse.json({ error: "Admin access required" }, { status: 403 });
    }
    
    return NextResponse.json(
      { error: "Failed to update user role" },
      { status: 500 }
    );
  }
}
