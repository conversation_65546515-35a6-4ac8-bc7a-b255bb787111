"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { generateSlug } from "@/lib/utils";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { X } from "lucide-react";

const restaurantTypes = [
  "Dine-in",
  "Food Truck",
  "Takeout",
  "Delivery",
  "Cafe",
  "Bar",
  "Buffet",
  "Food Court",
  "Other",
];

const commonHighlights = [
  "Outdoor Seating",
  "Wheelchair Accessible",
  "Free Wi-Fi",
  "Family-Friendly",
  "Live Music",
  "Happy Hour",
  "Vegan Options",
  "Gluten-Free Options",
  "Pet-Friendly",
  "Parking Available",
  "Reservations",
  "Delivery",
  "Takeout",
  "Catering",
  "Private Dining",
  "BYOB",
  "Full Bar",
  "Waterfront",
  "Rooftop",
  "Open Late",
];
const commonCuisines = [
  "American",
  "Italian",
  "Mexican",
  "Chinese",
  "Japanese",
  "Korean",
  "Indian",
  "Pakistani",
  "Bangladeshi",
];

export default function AddRestaurantPage() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [cuisines, setCuisines] = useState([]);
  const [cuisineInput, setCuisineInput] = useState("");
  const [highlights, setHighlights] = useState([]);
  const [highlightInput, setHighlightInput] = useState("");

  const handleAddCuisine = () => {
    if (
      cuisineInput.trim() &&
      cuisines.length < 5 &&
      !cuisines.includes(cuisineInput.trim())
    ) {
      setCuisines([...cuisines, cuisineInput.trim()]);
      setCuisineInput("");
    }
  };

  // this is not work
  const handleRemoveCuisine = (cuisine, e) => {
    e.stopPropagation(); // Prevent event bubbling
    setCuisines(cuisines.filter((c) => c !== cuisine));
  };

  const handleAddHighlight = () => {
    if (
      highlightInput.trim() &&
      highlights.length < 20 &&
      !highlights.includes(highlightInput.trim())
    ) {
      setHighlights([...highlights, highlightInput.trim()]);
      setHighlightInput("");
    }
  };

  const handleSelectHighlight = (highlight) => {
    if (highlights.length < 20 && !highlights.includes(highlight)) {
      setHighlights([...highlights, highlight]);
    }
  };

  const handleSelectCuisine = (cuisine) => {
    if (cuisines.length < 5 && !cuisines.includes(cuisine)) {
      setCuisines([...cuisines, cuisine]);
    }
  };

  const handleRemoveHighlight = (highlight, e) => {
    e.stopPropagation(); // Prevent event bubbling
    setHighlights(highlights.filter((h) => h !== highlight));
  };

  const handleCuisineKeyDown = (e) => {
    if (e.key === "Enter") {
      e.preventDefault(); // Prevent form submission
      handleAddCuisine();
    }
  };

  const handleHighlightKeyDown = (e) => {
    if (e.key === "Enter") {
      e.preventDefault(); // Prevent form submission
      handleAddHighlight();
    }
  };

  async function onSubmit(event) {
    event.preventDefault();
    setIsLoading(true);

    const formData = new FormData(event.currentTarget);
    const data = {
      name: formData.get("name"),
      address: formData.get("address"),
      city: formData.get("city"),
      state: formData.get("state"),
      zipcode: formData.get("zipcode"),
      type: formData.get("type"),
      phone: formData.get("phone") || null,
      website: formData.get("website") || null,
      cuisines: cuisines,
      highlights: highlights,
      slug: generateSlug(
        formData.get("name"),
        formData.get("city"),
        formData.get("address")
      ),
    };

    try {
      const response = await fetch("/api/restaurants", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        throw new Error("Failed to add restaurant");
      }

      router.push("/restaurants");
      router.refresh();
    } catch (error) {
      console.error("Error adding restaurant:", error);
      alert("Failed to add restaurant. Please try again.");
    } finally {
      setIsLoading(false);
    }
  }

  return (
    <div className="container mx-auto py-10">
      <Card className="max-w-2xl mx-auto">
        <CardHeader>
          <CardTitle>Add New Restaurant</CardTitle>
          <CardDescription>
            Fill in the details below to add a new restaurant to our directory.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={onSubmit} className="space-y-6">
            {/* Basic Info */}
            <div className="space-y-2">
              <Label htmlFor="name">Restaurant Name</Label>
              <Input
                id="name"
                name="name"
                required
                placeholder="Enter restaurant name"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="address">Address</Label>
              <Input
                id="address"
                name="address"
                required
                placeholder="Enter street address"
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="city">City</Label>
                <Input
                  id="city"
                  name="city"
                  required
                  placeholder="Enter city"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="state">State</Label>
                <Input
                  id="state"
                  name="state"
                  required
                  placeholder="Enter state"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="zipcode">ZIP Code</Label>
              <Input
                id="zipcode"
                name="zipcode"
                required
                placeholder="Enter ZIP code"
              />
            </div>

            {/* Contact Info */}
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="phone">Phone Number (Optional)</Label>
                <Input id="phone" name="phone" placeholder="(*************" />
              </div>
              <div className="space-y-2">
                <Label htmlFor="website">Website (Optional)</Label>
                <Input
                  id="website"
                  name="website"
                  placeholder="https://example.com"
                />
              </div>
            </div>

            {/* Restaurant Type */}
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="type">Restaurant Type</Label>
                <Select name="type">
                  <SelectTrigger>
                    <SelectValue placeholder="Select a type" />
                  </SelectTrigger>
                  <SelectContent>
                    {restaurantTypes.map((type) => (
                      <SelectItem key={type} value={type}>
                        {type}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Cuisines */}
            <div className="space-y-2">
              <Label>Cuisines (Optional, max 5)</Label>
              <div className="flex gap-2 mb-2 flex-wrap">
                {cuisines.map((cuisine) => (
                  <Badge
                    key={cuisine}
                    variant="secondary"
                    className="flex items-center gap-1"
                  >
                    {cuisine}
                    {/* adding a button here */}
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={(e) => handleRemoveCuisine(cuisine, e)}
                      className="h-3 w-3 cursor-pointer"
                    >
                      <X />
                    </Button>
                  </Badge>
                ))}
              </div>
              <div className="flex gap-2">
                <Input
                  value={cuisineInput}
                  onChange={(e) => setCuisineInput(e.target.value)}
                  onKeyDown={handleCuisineKeyDown}
                  placeholder="Add cuisine (e.g., Italian, Mexican)"
                  disabled={cuisines.length >= 5}
                />
                <Button
                  type="button"
                  onClick={handleAddCuisine}
                  disabled={cuisines.length >= 5 || !cuisineInput.trim()}
                >
                  Add
                </Button>
              </div>
              {/* Common cusisines suggestions */}
              <div className="mt-2">
                <p className="text-sm text-muted-foreground mb-2">
                  Common Cusisines:
                </p>
                <div className="flex flex-wrap gap-2">
                  {commonCuisines.slice(0, 10).map((cuisine) => (
                    <Badge
                      key={cuisine}
                      variant="outline"
                      className="cursor-pointer hover:bg-secondary"
                      onClick={() => handleSelectCuisine(cuisine)}
                    >
                      {cuisine}
                    </Badge>
                  ))}
                </div>
              </div>
              {cuisines.length >= 5 && (
                <p className="text-sm text-muted-foreground mt-1">
                  Maximum of 5 cuisines reached
                </p>
              )}
            </div>

            {/* Highlights */}
            <div className="space-y-2">
              <Label>Highlights & Features (Optional, max 20)</Label>
              <div className="flex gap-2 mb-2 flex-wrap">
                {highlights.map((highlight) => (
                  <Badge
                    key={highlight}
                    variant="outline"
                    className="flex items-center gap-1"
                  >
                    {highlight}
                    {/* adding a button here */}
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={(e) => handleRemoveHighlight(highlight, e)}
                      className="h-3 w-3 cursor-pointer"
                    >
                      <X />
                    </Button>
                  </Badge>
                ))}
              </div>
              <div className="flex gap-2">
                <Input
                  value={highlightInput}
                  onChange={(e) => setHighlightInput(e.target.value)}
                  onKeyDown={handleHighlightKeyDown}
                  placeholder="Add custom highlight"
                  disabled={highlights.length >= 20}
                />
                <Button
                  type="button"
                  onClick={handleAddHighlight}
                  disabled={highlights.length >= 20 || !highlightInput.trim()}
                >
                  Add
                </Button>
              </div>

              {/* Common highlights suggestions */}
              <div className="mt-2">
                <p className="text-sm text-muted-foreground mb-2">
                  Common highlights:
                </p>
                <div className="flex flex-wrap gap-2">
                  {commonHighlights.slice(0, 10).map((highlight) => (
                    <Badge
                      key={highlight}
                      variant="outline"
                      className="cursor-pointer hover:bg-secondary"
                      onClick={() => handleSelectHighlight(highlight)}
                    >
                      {highlight}
                    </Badge>
                  ))}
                </div>
              </div>

              {highlights.length >= 20 && (
                <p className="text-sm text-muted-foreground mt-1">
                  Maximum of 20 highlights reached
                </p>
              )}
            </div>

            <Button type="submit" className="w-full" disabled={isLoading}>
              {isLoading ? "Adding Restaurant..." : "Add Restaurant"}
            </Button>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
