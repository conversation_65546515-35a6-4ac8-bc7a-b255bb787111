import { notFound } from "next/navigation";
import prisma from "@/lib/prisma";
import { MapPin } from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Breadcrumb,
  BreadcrumbList,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbPage,
  BreadcrumbSeparator,
  BreadcrumbEllipsis,
 } from "@/components/ui/breadcrumb";

async function getRestaurant(slug) {
  const restaurant = await prisma.restaurant.findUnique({
    where: { slug },
  });

  if (!restaurant) return null;
  return restaurant;
}

const RestaurantPage = async ({ params }) => {
  // params are coming from [slug] / url
  const { slug } = await params;
  const restaurant = await getRestaurant(slug);

  if (!restaurant) {
    notFound();
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <Breadcrumb>
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink href="/">Home</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbItem>
            <BreadcrumbLink href="/restaurants">Restaurants</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbItem>
            <BreadcrumbPage>{restaurant.name}</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>
      <Card className="max-w-3xl mx-auto">
        <CardHeader>
          <CardTitle className="text-2xl md:text-3xl">
            {restaurant.name}
          </CardTitle>
          <div className="flex gap-2 mt-2">
            <Badge variant="secondary">Restaurant</Badge>
          </div>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="flex items-center text-muted-foreground">
            <MapPin className="w-5 h-5 mr-2" />
            <span className="text-lg capitalize">
              {restaurant.address}, {restaurant.city}, {restaurant.state}{" "}
              {restaurant.zipcode}
            </span>
          </div>

          <div className="pt-4 border-t">
            <h3 className="text-lg font-medium mb-2">Restaurant Details</h3>
            <p className="text-muted-foreground">
              This restaurant was added on{" "}
              {new Date(restaurant.createdAt).toLocaleDateString()}.
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default RestaurantPage;
