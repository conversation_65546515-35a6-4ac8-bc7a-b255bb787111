import { redirect } from "next/navigation";
import { getCurrentUser } from "@/lib/auth";
import prisma from "@/lib/prisma";
import { EditRestaurantForm } from "@/components/restaurant/EditRestaurantForm";

async function getRestaurant(slug) {
  try {
    const restaurant = await prisma.restaurant.findUnique({
      where: { slug },
      include: {
        owner: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true
          }
        }
      }
    });
    return restaurant;
  } catch (error) {
    console.error("Error fetching restaurant:", error);
    return null;
  }
}

export default async function EditRestaurantPage({ params }) {
  const { slug } = await params;
  const user = await getCurrentUser();
  
  if (!user) {
    redirect('/sign-in');
  }

  const restaurant = await getRestaurant(slug);
  
  if (!restaurant) {
    redirect('/404');
  }

  // Check permissions: Admin can edit all, Owner can edit their own
  const canEdit = user.role === 'ADMIN' || 
                  (user.role === 'OWNER' && restaurant.ownerId === user.id) ||
                  (restaurant.ownerId === user.id);

  if (!canEdit) {
    redirect('/restaurants');
  }

  return (
    <div className="container mx-auto py-8">
      <div className="max-w-4xl mx-auto">
        <div className="mb-6">
          <h1 className="text-3xl font-bold text-gray-900">Edit Restaurant</h1>
          <p className="text-gray-600 mt-2">
            Update the information for {restaurant.name}
          </p>
        </div>
        
        <EditRestaurantForm 
          restaurant={restaurant} 
          user={user}
        />
      </div>
    </div>
  );
}
