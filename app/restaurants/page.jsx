import RestaurantCard from "@/components/restaurant/restaurant-card";
import prisma from "@/lib/prisma";

const RestaurantsPage = async () => {
  const restaurants = await prisma.restaurant.findMany();

  return (
    <div className="container mx-auto px-4 py-4">
      RestaurantsPage
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {restaurants.map((restaurant) => {
          return <RestaurantCard key={restaurant.id} restaurant={restaurant} />;
        })}
      </div>
    </div>
  );
};

export default RestaurantsPage;
