import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Home, Search, ChefHat, ArrowLeft } from "lucide-react";

export default function NotFound() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-orange-50 via-white to-red-50 flex items-center justify-center px-4">
      <div className="max-w-2xl mx-auto text-center space-y-8">
        {/* 404 Illustration */}
        <div className="space-y-4">
          <div className="text-8xl md:text-9xl font-bold text-gray-200 select-none">
            404
          </div>
          <div className="text-6xl">🍽️</div>
        </div>

        {/* Error Message */}
        <div className="space-y-4">
          <h1 className="text-3xl md:text-4xl font-bold text-gray-900">
            Oops! This page is off the menu
          </h1>
          <p className="text-lg text-gray-600 max-w-md mx-auto">
            The page you're looking for doesn't exist. It might have been moved, 
            deleted, or you entered the wrong URL.
          </p>
        </div>

        {/* Action Cards */}
        <div className="grid gap-4 md:grid-cols-2 max-w-lg mx-auto">
          <Card className="hover:shadow-md transition-shadow">
            <CardContent className="p-6 text-center space-y-3">
              <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mx-auto">
                <Home className="w-6 h-6 text-primary" />
              </div>
              <h3 className="font-semibold">Go Home</h3>
              <p className="text-sm text-gray-600">
                Return to our homepage and start fresh
              </p>
              <Button asChild className="w-full">
                <Link href="/">
                  <Home className="w-4 h-4 mr-2" />
                  Home Page
                </Link>
              </Button>
            </CardContent>
          </Card>

          <Card className="hover:shadow-md transition-shadow">
            <CardContent className="p-6 text-center space-y-3">
              <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto">
                <Search className="w-6 h-6 text-green-600" />
              </div>
              <h3 className="font-semibold">Search Restaurants</h3>
              <p className="text-sm text-gray-600">
                Find restaurants by name or location
              </p>
              <Button asChild variant="outline" className="w-full">
                <Link href="/search">
                  <Search className="w-4 h-4 mr-2" />
                  Search
                </Link>
              </Button>
            </CardContent>
          </Card>
        </div>

        {/* Additional Actions */}
        <div className="space-y-4">
          <div className="flex flex-col sm:flex-row gap-3 justify-center">
            <Button asChild variant="outline">
              <Link href="/restaurants">
                <ChefHat className="w-4 h-4 mr-2" />
                Browse All Restaurants
              </Link>
            </Button>
            
            <Button asChild variant="ghost" onClick={() => window.history.back()}>
              <span className="cursor-pointer">
                <ArrowLeft className="w-4 h-4 mr-2" />
                Go Back
              </span>
            </Button>
          </div>

          {/* Help Text */}
          <div className="text-sm text-gray-500 space-y-2">
            <p>Still having trouble? Here are some suggestions:</p>
            <ul className="text-left max-w-md mx-auto space-y-1">
              <li>• Check the URL for typos</li>
              <li>• Use the search function to find what you're looking for</li>
              <li>• Browse our restaurant directory</li>
              <li>• Contact us if you think this is an error</li>
            </ul>
          </div>
        </div>

        {/* Contact Support */}
        <Card className="bg-gray-50 border-dashed">
          <CardContent className="p-6 text-center">
            <h4 className="font-medium mb-2">Need Help?</h4>
            <p className="text-sm text-gray-600 mb-4">
              If you believe this is an error or need assistance, please contact our support team.
            </p>
            <Button asChild variant="outline" size="sm">
              <Link href="/contact">
                Contact Support
              </Link>
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
