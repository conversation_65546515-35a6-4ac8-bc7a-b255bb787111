import { redirect } from "next/navigation";
import { getCurrentUser } from "@/lib/auth";
import prisma from "@/lib/prisma";
import RestaurantCard from "@/components/restaurant/restaurant-card";
import { Card, CardContent } from "@/components/ui/card";
import { Heart } from "lucide-react";

async function getUserFavorites(userId) {
  try {
    const favorites = await prisma.favorite.findMany({
      where: { userId },
      include: {
        restaurant: true
      },
      orderBy: { createdAt: 'desc' }
    });

    return favorites;
  } catch (error) {
    console.error("Error fetching favorites:", error);
    return [];
  }
}

export default async function FavoritesPage() {
  const user = await getCurrentUser();
  
  if (!user) {
    redirect('/sign-in');
  }

  const favorites = await getUserFavorites(user.id);

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4 flex items-center gap-3">
            <Heart className="w-8 h-8 text-red-500" />
            My Favorites
          </h1>
          <p className="text-lg text-gray-600">
            Your saved restaurants for easy access
          </p>
        </div>

        {/* Favorites Grid */}
        {favorites.length > 0 ? (
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            {favorites.map((favorite) => (
              <RestaurantCard 
                key={favorite.id} 
                restaurant={favorite.restaurant} 
                showFavoriteButton={true}
                isFavorited={true}
              />
            ))}
          </div>
        ) : (
          <Card className="p-12 text-center">
            <CardContent>
              <Heart className="w-16 h-16 text-gray-300 mx-auto mb-4" />
              <h3 className="text-xl font-semibold mb-2">No favorites yet</h3>
              <p className="text-gray-600 mb-6">
                Start exploring restaurants and save your favorites for easy access later.
              </p>
              <div className="space-y-2 text-sm text-gray-500">
                <p>• Browse restaurants and click the heart icon to save them</p>
                <p>• Your favorites will appear here for quick access</p>
                <p>• Perfect for planning your next dining experience</p>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}
