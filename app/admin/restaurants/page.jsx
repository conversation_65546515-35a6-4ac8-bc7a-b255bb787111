import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import Link from "next/link";
import prisma from "@/lib/prisma";
import { 
  Plus,
  Search,
  Edit,
  Eye,
  Star,
  MapPin,
  User
} from "lucide-react";

async function getRestaurants(searchParams) {
  const { search, status } = searchParams;
  
  try {
    const whereClause = {};
    
    if (search) {
      whereClause.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { city: { contains: search, mode: 'insensitive' } },
        { state: { contains: search, mode: 'insensitive' } }
      ];
    }
    
    if (status === 'published') {
      whereClause.isPublished = true;
    } else if (status === 'draft') {
      whereClause.isPublished = false;
    } else if (status === 'featured') {
      whereClause.isFeatured = true;
    }

    const restaurants = await prisma.restaurant.findMany({
      where: whereClause,
      include: {
        owner: {
          select: {
            firstName: true,
            lastName: true,
            email: true
          }
        },
        _count: {
          select: {
            favorites: true
          }
        }
      },
      orderBy: { createdAt: 'desc' }
    });

    return restaurants;
  } catch (error) {
    console.error("Error fetching restaurants:", error);
    return [];
  }
}

export default async function AdminRestaurantsPage({ searchParams }) {
  const params = await searchParams;
  const restaurants = await getRestaurants(params);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Restaurants</h1>
          <p className="text-gray-600 mt-2">
            Manage all restaurants in the directory
          </p>
        </div>
        <Button asChild>
          <Link href="/restaurants/add" className="flex items-center gap-2">
            <Plus className="w-4 h-4" />
            Add Restaurant
          </Link>
        </Button>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Filters</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex gap-4 items-center">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  placeholder="Search restaurants..."
                  className="pl-10"
                  defaultValue={params.search || ''}
                />
              </div>
            </div>
            <div className="flex gap-2">
              <Button 
                variant={!params.status ? "default" : "outline"} 
                size="sm"
                asChild
              >
                <Link href="/admin/restaurants">All</Link>
              </Button>
              <Button 
                variant={params.status === 'published' ? "default" : "outline"} 
                size="sm"
                asChild
              >
                <Link href="/admin/restaurants?status=published">Published</Link>
              </Button>
              <Button 
                variant={params.status === 'draft' ? "default" : "outline"} 
                size="sm"
                asChild
              >
                <Link href="/admin/restaurants?status=draft">Drafts</Link>
              </Button>
              <Button 
                variant={params.status === 'featured' ? "default" : "outline"} 
                size="sm"
                asChild
              >
                <Link href="/admin/restaurants?status=featured">Featured</Link>
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Restaurants List */}
      <Card>
        <CardHeader>
          <CardTitle>
            {restaurants.length} Restaurant{restaurants.length !== 1 ? 's' : ''}
          </CardTitle>
        </CardHeader>
        <CardContent>
          {restaurants.length > 0 ? (
            <div className="space-y-4">
              {restaurants.map((restaurant) => (
                <div key={restaurant.id} className="border rounded-lg p-4 hover:bg-gray-50 transition-colors">
                  <div className="flex items-start justify-between">
                    <div className="space-y-2 flex-1">
                      <div className="flex items-center gap-3">
                        <h3 className="font-semibold text-lg">{restaurant.name}</h3>
                        <div className="flex gap-2">
                          {restaurant.isFeatured && (
                            <Badge variant="default" className="flex items-center gap-1">
                              <Star className="w-3 h-3" />
                              Featured
                            </Badge>
                          )}
                          <Badge variant={restaurant.isPublished ? "default" : "secondary"}>
                            {restaurant.isPublished ? "Published" : "Draft"}
                          </Badge>
                        </div>
                      </div>
                      
                      <div className="flex items-center gap-4 text-sm text-gray-600">
                        <div className="flex items-center gap-1">
                          <MapPin className="w-4 h-4" />
                          {restaurant.city}, {restaurant.state}
                        </div>
                        {restaurant.owner && (
                          <div className="flex items-center gap-1">
                            <User className="w-4 h-4" />
                            {restaurant.owner.firstName} {restaurant.owner.lastName}
                          </div>
                        )}
                        <div className="flex items-center gap-1">
                          <Star className="w-4 h-4" />
                          {restaurant._count.favorites} favorites
                        </div>
                      </div>

                      {restaurant.cuisines.length > 0 && (
                        <div className="flex flex-wrap gap-1">
                          {restaurant.cuisines.slice(0, 3).map((cuisine) => (
                            <Badge key={cuisine} variant="outline" className="text-xs">
                              {cuisine}
                            </Badge>
                          ))}
                          {restaurant.cuisines.length > 3 && (
                            <Badge variant="outline" className="text-xs">
                              +{restaurant.cuisines.length - 3} more
                            </Badge>
                          )}
                        </div>
                      )}
                    </div>

                    <div className="flex items-center gap-2">
                      <Button asChild variant="outline" size="sm">
                        <Link href={`/restaurants/${restaurant.slug}`} target="_blank">
                          <Eye className="w-4 h-4" />
                        </Link>
                      </Button>
                      <Button asChild variant="outline" size="sm">
                        <Link href={`/admin/restaurants/${restaurant.id}/edit`}>
                          <Edit className="w-4 h-4" />
                        </Link>
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <p className="text-gray-500 mb-4">No restaurants found</p>
              <Button asChild>
                <Link href="/restaurants/add">Add First Restaurant</Link>
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
