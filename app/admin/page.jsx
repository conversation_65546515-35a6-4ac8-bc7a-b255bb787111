import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import Link from "next/link";
import prisma from "@/lib/prisma";
import {
  ChefHat,
  Users,
  Star,
  TrendingUp,
  Plus,
  Eye,
  Clock,
} from "lucide-react";

async function getAdminStats() {
  try {
    const [
      totalRestaurants,
      publishedRestaurants,
      featuredRestaurants,
      totalUsers,
      pendingRestaurants,
      totalFavorites,
    ] = await Promise.all([
      prisma.restaurant.count(),
      prisma.restaurant.count({ where: { isPublished: true } }),
      prisma.restaurant.count({ where: { isFeatured: true } }),
      prisma.user.count(),
      prisma.restaurant.count({ where: { isPublished: false } }),
      prisma.favorite.count(),
    ]);

    return {
      totalRestaurants,
      publishedRestaurants,
      featuredRestaurants,
      totalUsers,
      pendingRestaurants,
      totalFavorites,
    };
  } catch (error) {
    console.error("Error fetching admin stats:", error);
    return {
      totalRestaurants: 0,
      publishedRestaurants: 0,
      featuredRestaurants: 0,
      totalUsers: 0,
      pendingRestaurants: 0,
      totalFavorites: 0,
    };
  }
}

async function getRecentRestaurants() {
  try {
    return await prisma.restaurant.findMany({
      take: 5,
      orderBy: { createdAt: "desc" },
      include: {
        owner: {
          select: {
            firstName: true,
            lastName: true,
            email: true,
          },
        },
      },
    });
  } catch (error) {
    console.error("Error fetching recent restaurants:", error);
    return [];
  }
}

export default async function AdminDashboard() {
  const [stats, recentRestaurants] = await Promise.all([
    getAdminStats(),
    getRecentRestaurants(),
  ]);

  const statCards = [
    {
      title: "Total Restaurants",
      value: stats.totalRestaurants,
      icon: ChefHat,
      color: "text-blue-600",
      bgColor: "bg-blue-100",
    },
    {
      title: "Published",
      value: stats.publishedRestaurants,
      icon: Eye,
      color: "text-green-600",
      bgColor: "bg-green-100",
    },
    {
      title: "Featured",
      value: stats.featuredRestaurants,
      icon: Star,
      color: "text-yellow-600",
      bgColor: "bg-yellow-100",
    },
    {
      title: "Total Users",
      value: stats.totalUsers,
      icon: Users,
      color: "text-purple-600",
      bgColor: "bg-purple-100",
    },
    {
      title: "Pending Review",
      value: stats.pendingRestaurants,
      icon: Clock,
      color: "text-orange-600",
      bgColor: "bg-orange-100",
    },
    {
      title: "Total Favorites",
      value: stats.totalFavorites,
      icon: TrendingUp,
      color: "text-pink-600",
      bgColor: "bg-pink-100",
    },
  ];

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Admin Dashboard</h1>
          <p className="text-gray-600 mt-2">
            Manage your restaurant directory from here
          </p>
        </div>
        <Button asChild>
          <Link
            href="/admin/restaurants/new"
            className="flex items-center gap-2"
          >
            <Plus className="w-4 h-4" />
            Add Restaurant
          </Link>
        </Button>
      </div>

      {/* Stats Grid */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {statCards.map((stat) => (
          <Card key={stat.title}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">
                {stat.title}
              </CardTitle>
              <div className={`p-2 rounded-full ${stat.bgColor}`}>
                <stat.icon className={`w-4 h-4 ${stat.color}`} />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stat.value}</div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Recent Restaurants */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>Recent Restaurants</CardTitle>
            <Button asChild variant="outline" size="sm">
              <Link href="/admin/restaurants">View All</Link>
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {recentRestaurants.length > 0 ? (
            <div className="space-y-4">
              {recentRestaurants.map((restaurant) => (
                <div
                  key={restaurant.id}
                  className="flex items-center justify-between p-4 border rounded-lg"
                >
                  <div className="space-y-1">
                    <h3 className="font-medium">{restaurant.name}</h3>
                    <p className="text-sm text-gray-600">
                      {restaurant.city}, {restaurant.state}
                    </p>
                    {restaurant.owner && (
                      <p className="text-xs text-gray-500">
                        Owner: {restaurant.owner.firstName}{" "}
                        {restaurant.owner.lastName}
                      </p>
                    )}
                  </div>
                  <div className="flex items-center gap-2">
                    {restaurant.isFeatured && (
                      <Badge variant="default">Featured</Badge>
                    )}
                    <Badge
                      variant={restaurant.isPublished ? "default" : "secondary"}
                    >
                      {restaurant.isPublished ? "Published" : "Draft"}
                    </Badge>
                    <Button asChild variant="outline" size="sm">
                      <Link href={`/admin/restaurants/${restaurant.id}`}>
                        Edit
                      </Link>
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <p className="text-gray-500 text-center py-8">
              No restaurants found
            </p>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
