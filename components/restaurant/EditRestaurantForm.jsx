"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { X, Trash2, Save } from "lucide-react";
import { toast } from "sonner";

const restaurantTypes = [
  "Dine-in",
  "Food Truck",
  "Takeout",
  "Delivery",
  "Cafe",
  "Bar",
  "Buffet",
  "Food Court",
  "Other",
];

const commonCuisines = [
  "American",
  "Italian",
  "Mexican",
  "Chinese",
  "Japanese",
  "Korean",
  "Indian",
  "Pakistani",
  "Bangladeshi",
];

const commonHighlights = [
  "Outdoor Seating",
  "Wheelchair Accessible",
  "Free Wi-Fi",
  "Family-Friendly",
  "Live Music",
  "Happy Hour",
  "Vegan Options",
  "Gluten-Free Options",
  "Pet-Friendly",
  "Parking Available",
  "Reservations",
  "Delivery",
  "Takeout",
  "Catering",
  "Private Dining",
  "BYOB",
  "Full Bar",
  "Waterfront",
  "Rooftop",
  "Open Late",
];

export function EditRestaurantForm({ restaurant, user }) {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);

  // Form state
  const [formData, setFormData] = useState({
    name: restaurant.name,
    address: restaurant.address,
    city: restaurant.city,
    state: restaurant.state,
    zipcode: restaurant.zipcode,
    type: restaurant.type || "Dine-in",
    phone: restaurant.phone || "",
    website: restaurant.website || "",
    isPublished: restaurant.isPublished,
    isFeatured: restaurant.isFeatured,
  });

  const [cuisines, setCuisines] = useState(restaurant.cuisines || []);
  const [cuisineInput, setCuisineInput] = useState("");
  const [highlights, setHighlights] = useState(restaurant.highlights || []);
  const [highlightInput, setHighlightInput] = useState("");

  const canDelete = user.role === "ADMIN";
  const canFeature = user.role === "ADMIN";
  const canPublish = user.role === "ADMIN" || restaurant.ownerId === user.id;

  const handleInputChange = (field, value) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
  };

  const handleAddCuisine = () => {
    if (
      cuisineInput.trim() &&
      cuisines.length < 5 &&
      !cuisines.includes(cuisineInput.trim())
    ) {
      setCuisines([...cuisines, cuisineInput.trim()]);
      setCuisineInput("");
    }
  };

  const handleRemoveCuisine = (cuisine) => {
    setCuisines(cuisines.filter((c) => c !== cuisine));
  };

  const handleAddHighlight = () => {
    if (
      highlightInput.trim() &&
      highlights.length < 20 &&
      !highlights.includes(highlightInput.trim())
    ) {
      setHighlights([...highlights, highlightInput.trim()]);
      setHighlightInput("");
    }
  };

  const handleRemoveHighlight = (highlight) => {
    setHighlights(highlights.filter((h) => h !== highlight));
  };

  const handleSelectCuisine = (cuisine) => {
    if (cuisines.length < 5 && !cuisines.includes(cuisine)) {
      setCuisines([...cuisines, cuisine]);
    }
  };

  const handleSelectHighlight = (highlight) => {
    if (highlights.length < 20 && !highlights.includes(highlight)) {
      setHighlights([...highlights, highlight]);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      const updateData = {
        ...formData,
        cuisines,
        highlights,
      };

      const response = await fetch(`/api/restaurants/${restaurant.id}`, {
        method: "PUT",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(updateData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to update restaurant");
      }

      const updatedRestaurant = await response.json();
      toast.success("Restaurant updated successfully!");
      router.push(`/restaurants/${updatedRestaurant.slug}`);
      router.refresh();
    } catch (error) {
      console.error("Error updating restaurant:", error);
      toast.error(error.message || "Failed to update restaurant");
    } finally {
      setIsLoading(false);
    }
  };

  const handleDelete = async () => {
    setIsDeleting(true);

    try {
      const response = await fetch(`/api/restaurants/${restaurant.id}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to delete restaurant");
      }

      toast.success("Restaurant deleted successfully!");
      router.push("/restaurants");
      router.refresh();
    } catch (error) {
      console.error("Error deleting restaurant:", error);
      toast.error(error.message || "Failed to delete restaurant");
    } finally {
      setIsDeleting(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Header with actions */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-xl font-semibold">Restaurant Information</h2>
          <p className="text-sm text-gray-600">
            Owner:{" "}
            {restaurant.owner
              ? `${restaurant.owner.firstName} ${restaurant.owner.lastName}`
              : "Admin"}
          </p>
        </div>

        <div className="flex items-center gap-3">
          {canDelete && (
            <AlertDialog>
              <AlertDialogTrigger asChild>
                <Button variant="destructive" size="sm">
                  <Trash2 className="w-4 h-4 mr-2" />
                  Delete
                </Button>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>Delete Restaurant</AlertDialogTitle>
                  <AlertDialogDescription>
                    Are you sure you want to delete "{restaurant.name}"? This
                    action cannot be undone.
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel>Cancel</AlertDialogCancel>
                  <AlertDialogAction
                    onClick={handleDelete}
                    disabled={isDeleting}
                    className="bg-red-600 hover:bg-red-700"
                  >
                    {isDeleting ? "Deleting..." : "Delete"}
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          )}

          <Button onClick={() => router.back()} variant="outline">
            Cancel
          </Button>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>Basic Information</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="name">Restaurant Name</Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => handleInputChange("name", e.target.value)}
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="type">Type</Label>
                <Select
                  value={formData.type}
                  onValueChange={(value) => handleInputChange("type", value)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {restaurantTypes.map((type) => (
                      <SelectItem key={type} value={type}>
                        {type}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="address">Address</Label>
              <Input
                id="address"
                value={formData.address}
                onChange={(e) => handleInputChange("address", e.target.value)}
                required
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label htmlFor="city">City</Label>
                <Input
                  id="city"
                  value={formData.city}
                  onChange={(e) => handleInputChange("city", e.target.value)}
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="state">State</Label>
                <Input
                  id="state"
                  value={formData.state}
                  onChange={(e) => handleInputChange("state", e.target.value)}
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="zipcode">ZIP Code</Label>
                <Input
                  id="zipcode"
                  value={formData.zipcode}
                  onChange={(e) => handleInputChange("zipcode", e.target.value)}
                  required
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="phone">Phone</Label>
                <Input
                  id="phone"
                  value={formData.phone}
                  onChange={(e) => handleInputChange("phone", e.target.value)}
                  placeholder="(*************"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="website">Website</Label>
                <Input
                  id="website"
                  value={formData.website}
                  onChange={(e) => handleInputChange("website", e.target.value)}
                  placeholder="https://example.com"
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Status Controls */}
        <Card>
          <CardHeader>
            <CardTitle>Status & Visibility</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {canPublish && (
              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="published">Published</Label>
                  <p className="text-sm text-gray-600">
                    Make this restaurant visible to the public
                  </p>
                </div>
                <Switch
                  id="published"
                  checked={formData.isPublished}
                  onCheckedChange={(checked) =>
                    handleInputChange("isPublished", checked)
                  }
                />
              </div>
            )}

            {canFeature && (
              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="featured">Featured</Label>
                  <p className="text-sm text-gray-600">
                    Show this restaurant in featured sections
                  </p>
                </div>
                <Switch
                  id="featured"
                  checked={formData.isFeatured}
                  onCheckedChange={(checked) =>
                    handleInputChange("isFeatured", checked)
                  }
                />
              </div>
            )}
          </CardContent>
        </Card>

        {/* Cuisines */}
        <Card>
          <CardHeader>
            <CardTitle>Cuisines (Max 5)</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex flex-wrap gap-2 mb-4">
              {cuisines.map((cuisine) => (
                <Badge
                  key={cuisine}
                  variant="secondary"
                  className="flex items-center gap-1"
                >
                  {cuisine}
                  <X
                    className="h-3 w-3 cursor-pointer pointer-events-auto"
                    onClick={() => handleRemoveCuisine(cuisine)}
                  />
                </Badge>
              ))}
            </div>

            <div className="flex gap-2">
              <Input
                value={cuisineInput}
                onChange={(e) => setCuisineInput(e.target.value)}
                onKeyDown={(e) =>
                  e.key === "Enter" && (e.preventDefault(), handleAddCuisine())
                }
                placeholder="Add cuisine"
                disabled={cuisines.length >= 5}
              />
              <Button
                type="button"
                onClick={handleAddCuisine}
                disabled={cuisines.length >= 5 || !cuisineInput.trim()}
              >
                Add
              </Button>
            </div>

            <div>
              <p className="text-sm text-gray-600 mb-2">Quick add:</p>
              <div className="flex flex-wrap gap-2">
                {commonCuisines.map((cuisine) => (
                  <Badge
                    key={cuisine}
                    variant="outline"
                    className="cursor-pointer hover:bg-secondary"
                    onClick={() => handleSelectCuisine(cuisine)}
                  >
                    {cuisine}
                  </Badge>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Highlights */}
        <Card>
          <CardHeader>
            <CardTitle>Highlights & Features (Max 20)</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex flex-wrap gap-2 mb-4">
              {highlights.map((highlight) => (
                <Badge
                  key={highlight}
                  variant="outline"
                  className="flex items-center gap-1"
                >
                  {highlight}
                  <X
                    className="h-3 w-3 cursor-pointer pointer-events-auto"
                    onClick={() => handleRemoveHighlight(highlight)}
                  />
                </Badge>
              ))}
            </div>

            <div className="flex gap-2">
              <Input
                value={highlightInput}
                onChange={(e) => setHighlightInput(e.target.value)}
                onKeyDown={(e) =>
                  e.key === "Enter" &&
                  (e.preventDefault(), handleAddHighlight())
                }
                placeholder="Add highlight"
                disabled={highlights.length >= 20}
              />
              <Button
                type="button"
                onClick={handleAddHighlight}
                disabled={highlights.length >= 20 || !highlightInput.trim()}
              >
                Add
              </Button>
            </div>

            <div>
              <p className="text-sm text-gray-600 mb-2">Quick add:</p>
              <div className="flex flex-wrap gap-2">
                {commonHighlights.slice(0, 10).map((highlight) => (
                  <Badge
                    key={highlight}
                    variant="outline"
                    className="cursor-pointer hover:bg-secondary"
                    onClick={() => handleSelectHighlight(highlight)}
                  >
                    {highlight}
                  </Badge>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Save Button */}
        <div className="flex justify-end">
          <Button type="submit" disabled={isLoading} className="min-w-32">
            {isLoading ? (
              "Saving..."
            ) : (
              <>
                <Save className="w-4 h-4 mr-2" />
                Save Changes
              </>
            )}
          </Button>
        </div>
      </form>
    </div>
  );
}
