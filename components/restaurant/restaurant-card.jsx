import Link from "next/link";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "../ui/card";
import { Badge } from "@/components/ui/badge";
import { ChevronRight, MapPin, Star, ChefHat } from "lucide-react";

const RestaurantCard = ({ restaurant }) => {
  return (
    <Link href={`/restaurants/${restaurant.slug}`}>
      <Card className="hover:shadow-md transition-all duration-200 group hover:border-primary/20">
        <CardHeader>
          <div className="flex items-start justify-between">
            <div className="space-y-2">
              <CardTitle className="group-hover:text-primary transition-colors">
                {restaurant.name}
              </CardTitle>
              <div className="flex flex-wrap gap-2">
                <Badge variant="secondary" className="flex items-center gap-1">
                  <ChefHat className="w-3 h-3" />
                  {restaurant.type || "Restaurant"}
                </Badge>
                {restaurant.isFeatured && (
                  <Badge variant="default" className="flex items-center gap-1">
                    <Star className="w-3 h-3" />
                    Featured
                  </Badge>
                )}
                {restaurant.cuisines && restaurant.cuisines.length > 0 && (
                  <Badge variant="outline">
                    {restaurant.cuisines[0]}
                    {restaurant.cuisines.length > 1 &&
                      ` +${restaurant.cuisines.length - 1}`}
                  </Badge>
                )}
              </div>
            </div>
            <ChevronRight className="w-5 h-5 text-muted-foreground group-hover:text-primary transition-colors" />
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-3 text-muted-foreground">
            <div className="flex items-center text-sm">
              <MapPin className="w-4 h-4 mr-2" />
              <span>
                {restaurant.city}, {restaurant.state}
              </span>
            </div>

            {restaurant.highlights && restaurant.highlights.length > 0 && (
              <div className="flex flex-wrap gap-1">
                {restaurant.highlights.slice(0, 3).map((highlight) => (
                  <Badge key={highlight} variant="outline" className="text-xs">
                    {highlight}
                  </Badge>
                ))}
                {restaurant.highlights.length > 3 && (
                  <Badge variant="outline" className="text-xs">
                    +{restaurant.highlights.length - 3} more
                  </Badge>
                )}
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </Link>
  );
};

export default RestaurantCard;
