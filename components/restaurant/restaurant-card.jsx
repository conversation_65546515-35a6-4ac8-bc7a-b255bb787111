import Link from "next/link";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Tit<PERSON> } from "../ui/card";
import { Badge } from "@/components/ui/badge";
import { ChevronRight, MapPin } from "lucide-react";

const RestaurantCard = ({ restaurant }) => {
  return (
    <Link href={`/restaurants/${restaurant.slug}`}>
      <Card className="hover:shadow-md transition-all duration-200 group-hover:border-blue-200">
        <CardHeader>
          <div className="flex items-start justify-between">
            {/* card title with badge */}
            <div>
              <CardTitle className="group-hover:text-blue-600 transition-colors">
                {restaurant.name}
              </CardTitle>
              <div className="flex gap-2 mt-2">
                <Badge variant="secondary">type</Badge>
                <Badge variant="outline">cuisine</Badge>
              </div>
            </div>
            <ChevronRight className="w-5 h-5 text-muted-foreground group-hover:text-blue-600 transition-colors" />
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-2 text-muted-foreground">
            <div className="flex items-center text-sm">
              <MapPin className="w-4 h-4 mr-2" />
              <span className="capitalize">
                {restaurant.address}, {restaurant.city}, {restaurant.state}
              </span>
            </div>
          </div>
        </CardContent>
      </Card>
    </Link>
  );
};

export default RestaurantCard;
