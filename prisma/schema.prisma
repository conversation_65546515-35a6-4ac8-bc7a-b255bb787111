// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider  = "postgresql"
  url       = env("DATABASE_URL")
  directUrl = env("DIRECT_URL")
}

model Restaurant {
  id          String   @id @default(cuid())
  name        String
  address     String
  city        String
  state       String
  zipcode     String
  slug        String   @unique
  type        String?  @default("Dine-in")
  phone       String?
  website     String?
  cuisines    String[]
  highlights  String[]
  lat         Float?
  lng         Float?
  isPublished Boolean  @default(false)
  isFeatured  Boolean  @default(false)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@index([slug])
}
