import { z } from "zod";

export const restaurantSchema = z.object({
  name: z
    .string()
    .min(2, { message: "name must be at least 2 characters." })
    .max(100, { message: "name must be less than 100 characters" }),
  address: z
    .string()
    .min(2, { message: "address must be at least 2 characters." })
    .max(100, { message: "address must be less than 100 characters" }),
  city: z
    .string()
    .min(2, { message: "city must be at least 2 characters." })
    .max(100, { message: "city must be less than 100 characters" }),
  state: z.string().length(2, "State must be 2 characters"),
  zipcode: z
    .string()
    .regex(/^\d{5}(-\d{4})?$/, "Zipcode must be in format 12345 or 12345-6789"),
  type: z.string().optional().nullable(),
  phone: z.string().optional().nullable(),
  website: z.string().url("Must be a valid URL").optional().nullable(),
  cuisines: z
    .array(z.string())
    .max(5, "Maximum of 5 cuisines")
    .optional()
    .default([]),
  highlights: z
    .array(z.string())
    .max(20, "Maximum of 20 highlights")
    .optional()
    .default([]),
});
