import { auth, currentUser } from '@clerk/nextjs/server';
import prisma from '@/lib/prisma';

export async function getCurrentUser() {
  const { userId } = await auth();
  
  if (!userId) {
    return null;
  }

  const clerkUser = await currentUser();
  if (!clerkUser) {
    return null;
  }

  // Find or create user in our database
  let user = await prisma.user.findUnique({
    where: { clerkId: userId }
  });

  if (!user) {
    user = await prisma.user.create({
      data: {
        clerkId: userId,
        email: clerkUser.emailAddresses[0]?.emailAddress || '',
        firstName: clerkUser.firstName,
        lastName: clerkUser.lastName,
        role: 'USER' // Default role
      }
    });
  }

  return user;
}

export async function requireAuth() {
  const user = await getCurrentUser();
  if (!user) {
    throw new Error('Authentication required');
  }
  return user;
}

export async function requireRole(requiredRole) {
  const user = await requireAuth();
  
  if (user.role !== requiredRole && user.role !== 'ADMIN') {
    throw new Error(`${requiredRole} role required`);
  }
  
  return user;
}

export async function requireAdmin() {
  return await requireRole('ADMIN');
}

export async function requireOwnerOrAdmin() {
  const user = await requireAuth();
  
  if (user.role !== 'OWNER' && user.role !== 'ADMIN') {
    throw new Error('Owner or Admin role required');
  }
  
  return user;
}

export function hasRole(user, role) {
  if (!user) return false;
  return user.role === role || user.role === 'ADMIN';
}

export function isAdmin(user) {
  return hasRole(user, 'ADMIN');
}

export function isOwner(user) {
  return hasRole(user, 'OWNER');
}

export function canManageRestaurant(user, restaurant) {
  if (!user || !restaurant) return false;
  
  // Admin can manage all restaurants
  if (user.role === 'ADMIN') return true;
  
  // Owner can manage their own restaurants
  if (user.role === 'OWNER' && restaurant.ownerId === user.id) return true;
  
  return false;
}
