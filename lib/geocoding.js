export default async function getGeolocation(address, city, state) {
  try {
    const fullAddress = `${address}, ${city}, ${state}`;

    const url = `https://nominatim.openstreetmap.org/search?format=json&limit=1&q=${encodeURIComponent(
      fullAddress
    )}`;

    const response = await fetch(url, {
      headers: {
        "User-Agent": "FoodFinder/1.0",
      },
      cache: "no-store",
    });

    if (!response.ok) {
      throw new Error("Failed to fetch geolocation");
    }

    const data = await response.json();
    if (data && data.length > 0) {
      const result = data[0];
      console.log("Geolocation fetched successfully");
      return {
        lat: parseFloat(result.lat),
        lng: parseFloat(result.lon),
      };
    } else {
      console.log("Failed to fetch geolocation");
      return { lat: null, lng: null };
    }
  } catch (error) {
    console.error("Error fetching geolocation:", error);
    return { lat: null, lng: null };
  }
}
